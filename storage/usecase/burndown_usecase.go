package usecase

import (
	"excelize-test/models"
	"excelize-test/storage/repository/mysql"
	"fmt"
	"log"
	"strings"
	"time"
)

type BurndownUsecase struct {
	burndownRepo  *mysql.BurndownRepository
	issueRepo     *mysql.IssueRepository
	timesheetRepo *mysql.TimesheetRepository
}

func NewBurndownUsecase() *BurndownUsecase {
	return &BurndownUsecase{
		burndownRepo: mysql.NewBurndownRepository(),
		issueRepo:    mysql.NewIssueRepository(),
	}
}

func (s *BurndownUsecase) CalculateBurndownForEpic(epicID string, issues []mysql.Issue, startYear, startWeek, endYear, endWeek int) (models.BurndownRecords, error) {
	epicIssues := s.filterIssuesForEpic(issues, epicID)
	totalEstimate := s.calculateTotalEstimate(epicIssues)
	log.Printf("issues found for epic %s %d ", epicID, len(epicIssues))
	log.Printf("total estimate for epic %s: %f", epicID, totalEstimate)

	allStatusChanges, err := s.loadAllStatusChangesForIssues(epicIssues)
	if err != nil {
		return nil, fmt.Errorf("failed to load status changes: %v", err)
	}

	var burndownRecords models.BurndownRecords

	for year := startYear; year <= endYear; year++ {
		weekStart := 1
		weekEnd := 52

		if year == startYear {
			weekStart = startWeek
		}
		if year == endYear {
			weekEnd = endWeek
		}

		for week := weekStart; week <= weekEnd; week++ {
			actual := s.calculateActualRemainingForWeekOptimized(epicIssues, allStatusChanges, year, week)
			workCompleted := s.calculateWorkCompletedByWeek(epicID, year, week)
			ideal := totalEstimate - workCompleted
			if ideal < 0 {
				ideal = 0
			}

			// Debug logging for first few weeks
			if week <= 3 || (week >= 25 && week <= 27) {
				log.Printf("DEBUG Week %d-%d: totalEstimate=%.2f, workCompleted=%.2f, ideal=%.2f",
					year, week, totalEstimate, workCompleted, ideal)
			}

			record := models.BurndownRecord{
				PK:     fmt.Sprintf("%s_%d_%d", epicID, year, week),
				EpicID: epicID,
				Year:   year,
				Week:   week,
				Actual: actual,
				Ideal:  ideal,
			}

			burndownRecords = append(burndownRecords, record)
		}
	}

	return burndownRecords, nil
}

func (s *BurndownUsecase) filterIssuesForEpic(issues []mysql.Issue, epicID string) []mysql.Issue {
	var epicIssues []mysql.Issue

	for _, issue := range issues {
		if issue.Key == epicID || issue.ParentKey == epicID {
			epicIssues = append(epicIssues, issue)
		}
	}

	return epicIssues
}

func (s *BurndownUsecase) calculateTotalEstimate(issues []mysql.Issue) float64 {
	total := 0.0
	for _, issue := range issues {
		total += float64(issue.TimeOriginalEstimate) / 3600.0
	}
	return total
}

func (s *BurndownUsecase) isTaskCompleted(statusName string) bool {
	completedStatuses := []string{"Done Develop*", "Completed*", "Testing*", "Done", "Done(Develop)", "To Test (Staging)", "Test (Staging)", "Released (Production)", "Closed", "Done (Develop)", "Released(Production)"}
	statusLower := strings.ToLower(statusName)

	for _, status := range completedStatuses {
		if strings.ToLower(status) == statusLower {
			return true
		}
	}

	return false
}

func (s *BurndownUsecase) calculateWorkCompletedByWeek(epicID string, targetYear, targetWeek int) float64 {
	var timesheetRecords []mysql.TimesheetRecord

	query := mysql.Db.Where("epic = ?", epicID)
	// get all records from the same year before the target week (not including the target week itself)
	// Only include records from the same year to avoid including previous years' work
	query = query.Where(
		"CAST(year AS UNSIGNED) = ? AND CAST(week AS UNSIGNED) < ?",
		targetYear, targetWeek,
	)

	err := query.Find(&timesheetRecords).Error
	if err != nil {
		log.Printf("Error querying timesheet records for epic %s: %v", epicID, err)
		return 0.0
	}

	// Debug logging for first few weeks
	if targetWeek <= 3 || (targetWeek >= 25 && targetWeek <= 27) {
		log.Printf("DEBUG: Found %d timesheet records for epic %s before week %d-%d",
			len(timesheetRecords), epicID, targetYear, targetWeek)
		for _, record := range timesheetRecords {
			log.Printf("DEBUG: Record - Year: %s, Week: %s, Effort: %.2f",
				record.Year, record.Week, record.EstimateEffort)
		}
	}

	totalWork := 0.0
	const HOURS = 40
	for _, record := range timesheetRecords {
		totalWork += record.EstimateEffort * HOURS
	}
	log.Printf("Total work completed for epic %s in year %d before week %d (not including week %d): %.2f hours", epicID, targetYear, targetWeek, targetWeek, totalWork)

	return totalWork
}



func (s *BurndownUsecase) SaveBurndownData(epicID string, records models.BurndownRecords) error {
	return s.burndownRepo.Update(epicID, records)
}

func (s *BurndownUsecase) GetBurndownData(epicID string) (models.BurndownRecords, error) {
	return s.burndownRepo.GetByEpic(epicID)
}

func (s *BurndownUsecase) getWeekStart(year, week int) time.Time {

	jan1 := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)

	daysToMonday := (8 - int(jan1.Weekday())) % 7
	if jan1.Weekday() == time.Sunday {
		daysToMonday = 1
	}
	firstMonday := jan1.AddDate(0, 0, daysToMonday)

	targetWeekStart := firstMonday.AddDate(0, 0, (week-1)*7)

	return targetWeekStart
}

func (s *BurndownUsecase) getWeekEnd(year, week int) time.Time {

	targetWeekStart := s.getWeekStart(year, week)
	weekEnd := targetWeekStart.AddDate(0, 0, 6).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	return weekEnd
}

func (s *BurndownUsecase) loadAllStatusChangesForIssues(issues []mysql.Issue) (map[string][]mysql.StatusChange, error) {
	if len(issues) == 0 {
		return make(map[string][]mysql.StatusChange), nil
	}

	// Extract all issue keys
	var issueKeys []string
	for _, issue := range issues {
		issueKeys = append(issueKeys, issue.Key)
	}

	// Single query to get ALL status changes for ALL issues
	var allChanges []mysql.StatusChange
	err := mysql.Db.Where("`key` IN ?", issueKeys).Order("'key' ASC, date ASC").Find(&allChanges).Error
	if err != nil {
		return nil, err
	}

	// Group by issue key
	changesByKey := make(map[string][]mysql.StatusChange)
	for _, change := range allChanges {
		changesByKey[change.Key] = append(changesByKey[change.Key], change)
	}

	log.Printf("Loaded %d status changes for %d issues in 1 query", len(allChanges), len(issues))
	return changesByKey, nil
}

func (s *BurndownUsecase) calculateActualRemainingForWeekOptimized(issues []mysql.Issue, allStatusChanges map[string][]mysql.StatusChange, targetYear, targetWeek int) float64 {
	remaining := 0.0
	weekStartTimestamp := uint64(s.getWeekStart(targetYear, targetWeek).UnixNano())
	weekEndTimestamp := uint64(s.getWeekEnd(targetYear, targetWeek).UnixNano())

	for _, issue := range issues {
		// Get pre-loaded status changes for this issue
		statusChanges := allStatusChanges[issue.Key]

		// Check if issue was created AFTER week start
		// Use FIRST status change as creation date (regardless of status)
		issueExistedAtWeekStart := true
		if len(statusChanges) > 0 {
			firstChange := statusChanges[0] // Already sorted by date ASC
			if firstChange.Date > weekStartTimestamp {
				// Issue was created after week start
				issueExistedAtWeekStart = false
				creationTime := time.Unix(0, int64(firstChange.Date))
				log.Printf("DEBUG: Issue %s was created after week %d-%d start (first change: %s at %s)",
					issue.Key, targetYear, targetWeek, firstChange.ToStatusName, creationTime.Format("2006-01-02 15:04:05"))
			} else {
				log.Printf("DEBUG: Issue %s existed at week %d-%d start (first change before week start)",
					issue.Key, targetYear, targetWeek)
			}
		} else {
			// No status changes - assume issue existed before tracking started
			log.Printf("DEBUG: Issue %s has no status changes - assuming existed at week %d-%d start",
				issue.Key, targetYear, targetWeek)
		}

		// If issue didn't exist at week start, skip it
		if !issueExistedAtWeekStart {
			log.Printf("DEBUG: Issue %s - Skipping (didn't exist at week start)", issue.Key)
			continue
		}

		// Filter changes that happened before end of week
		var changesBeforeWeek []mysql.StatusChange
		for _, change := range statusChanges {
			if change.Date <= weekEndTimestamp {
				changesBeforeWeek = append(changesBeforeWeek, change)
			}
		}

		// Find latest status by end of week
		isCompletedByWeekEnd := false
		if len(changesBeforeWeek) > 0 {
			latestChange := changesBeforeWeek[len(changesBeforeWeek)-1] // Already sorted by date
			if s.isTaskCompleted(latestChange.ToStatusName) {
				isCompletedByWeekEnd = true
				log.Printf("DEBUG: Issue %s - Latest status by week %d-%d end: %s (completed)",
					issue.Key, targetYear, targetWeek, latestChange.ToStatusName)
			} else {
				log.Printf("DEBUG: Issue %s - Latest status by week %d-%d end: %s (not completed)",
					issue.Key, targetYear, targetWeek, latestChange.ToStatusName)
			}
		} else {
			// No status changes, use current status
			if s.isTaskCompleted(issue.StatusName) {
				isCompletedByWeekEnd = true
				log.Printf("DEBUG: Issue %s - Current status: %s (completed, no historical data)",
					issue.Key, issue.StatusName)
			} else {
				log.Printf("DEBUG: Issue %s - Current status: %s (not completed, no historical data)",
					issue.Key, issue.StatusName)
			}
		}

		// Add to remaining if not completed
		if !isCompletedByWeekEnd {
			hours := float64(issue.TimeOriginalEstimate) / 3600.0
			remaining += hours
			log.Printf("DEBUG: Issue %s - Adding %.2f hours to remaining work", issue.Key, hours)
		}
	}

	log.Printf("DEBUG: Week %d-%d total remaining: %.2f hours", targetYear, targetWeek, remaining)
	return remaining
}
